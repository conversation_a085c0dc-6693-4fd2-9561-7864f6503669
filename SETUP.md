# Web Agent - Basic Frontend Setup

This project consists of a Next.js frontend and Express.js backend that allows users to submit prompts and receive responses.

## Project Structure

```
Web-Agent/
├── frontend/          # Next.js frontend (TypeScript + Tailwind CSS)
├── backend/           # Express.js backend
└── SETUP.md          # This file
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the backend server:
   ```bash
   npm start
   ```

   The backend will run on `http://localhost:3001`

### Frontend Setup

1. Open a new terminal and navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

   The frontend will run on `http://localhost:3000`

## Usage

1. Make sure both backend and frontend servers are running
2. Open your browser and go to `http://localhost:3000`
3. Enter a prompt in the text area
4. Click "Submit Prompt" to send it to the backend
5. The response will appear below the form

## API Endpoints

### POST /api/prompt
- **Description**: Accepts a user prompt and returns a response
- **Request Body**: 
  ```json
  {
    "prompt": "Your prompt text here"
  }
  ```
- **Response**: 
  ```json
  {
    "reply": "You said: Your prompt text here"
  }
  ```

## Features

- ✅ Clean, responsive UI with Tailwind CSS
- ✅ TypeScript support for type safety
- ✅ CORS enabled for frontend-backend communication
- ✅ Loading states and error handling
- ✅ Form validation
- ✅ Dark mode support

## Next Steps

You can extend this basic setup by:
- Adding authentication
- Integrating with AI services (OpenAI, etc.)
- Adding a database for storing prompts/responses
- Implementing real-time features with WebSockets
- Adding more sophisticated prompt processing
