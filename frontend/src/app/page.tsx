'use client';

import { useState } from 'react';
import TextInput from '../components/TextInput';

export default function Home() {
  const [response, setResponse] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const handlePromptSubmit = async (prompt: string) => {
    if (!prompt.trim()) return;

    setLoading(true);
    setError('');

    try {
      const res = await fetch('http://localhost:3001/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      setResponse(data.reply);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error submitting prompt:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            Prompt Interface
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Enter your prompt below and get a response from the backend.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <TextInput onSubmit={handlePromptSubmit} />

          {loading && (
            <div className="mt-4 text-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Processing...</p>
            </div>
          )}

          {error && (
            <div className="mt-4 p-3 bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-300 rounded">
              <p className="text-sm">Error: {error}</p>
            </div>
          )}

          {response && !loading && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Response:</h3>
              <p className="text-blue-700 dark:text-blue-300">{response}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
