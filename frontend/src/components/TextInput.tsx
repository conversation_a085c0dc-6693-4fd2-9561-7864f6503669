import { useState } from 'react';

interface Props {
  onSubmit: (prompt: string) => void;
}

export default function TextInput({ onSubmit }: Props) {
  const [prompt, setPrompt] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(prompt);
    setPrompt('');
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder="Enter your prompt"
        className="input"
      />
      <button type="submit" className="button">Submit</button>
    </form>
  );
}
