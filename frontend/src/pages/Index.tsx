import { useState } from 'react';
import TextInput from '@/components/TextInput';

export default function Home() {
  const [response, setResponse] = useState('');

  const handlePromptSubmit = async (prompt: string) => {
    try {
      const res = await fetch('http://localhost:5000/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });
      const data = await res.json();
      setResponse(data.reply);
    } catch (error) {
      setResponse('Error contacting backend');
    }
  };

  return (
    <main className="container">
      <h1>Prompt App</h1>
      <TextInput onSubmit={handlePromptSubmit} />
      {response && <p><strong>Response:</strong> {response}</p>}
    </main>
  );
}
